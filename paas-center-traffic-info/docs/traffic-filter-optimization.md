# 流量数据过滤优化方案

## 问题背景

在 `PadTrafficInfoServiceImpl#sendMessage` 方法中，当前对所有传入的 `padOutCode` 都需要通过第210行的接口获取 `Pad` 信息来获取 `customerId`。现在需要对 `customerId` 为3的数据进行过滤，但由于该接口的瞬时并发在万级以上，需要优化以减少不必要的远程接口调用。

## 优化方案

### 方案一：增加 customerId 专用缓存

1. **Redis 缓存层**：新增 `PAD_OUT_CODE_CUSTOMER_ID_KEY` 缓存key，专门存储 `padOutCode` 到 `customerId` 的映射
2. **本地缓存层**：使用 Guava Cache 作为本地缓存，进一步减少 Redis 访问
3. **多级缓存策略**：本地缓存 -> Redis缓存 -> 远程接口调用

### 方案二：配置化过滤规则

1. **配置化管理**：通过 `traffic.filter.customer.ids` 配置项管理需要过滤的客户ID列表
2. **动态刷新**：支持通过 `@RefreshScope` 注解动态刷新配置
3. **灵活扩展**：支持多个客户ID的过滤，用逗号分隔

### 方案三：管理接口

提供管理接口用于：
- 刷新过滤配置
- 查看当前过滤配置
- 检查指定客户ID是否被过滤

## 实现细节

### 1. 缓存结构

```
Redis Key: pad_out_code_customer_id:{padOutCode}
Value: customerId (Long)
TTL: 24小时
```

### 2. 本地缓存

```java
Cache<String, Long> customerIdCache = CacheBuilder.newBuilder()
    .maximumSize(50000)
    .expireAfterWrite(10, TimeUnit.MINUTES)
    .build();
```

### 3. 过滤逻辑

```java
// 快速过滤：优先检查customerId缓存
Long customerId = getCustomerIdByPadOutCode(dto.getPadOutCode());
if (shouldFilterCustomerId(customerId)) {
    return true; // 直接返回，不处理数据
}
```

## 性能优化效果

### 优化前
- 每次请求都可能需要调用远程接口 `padInternalFacade.getPadByOutCodeAndIp()`
- 高并发场景下远程调用成为性能瓶颈

### 优化后
1. **命中本地缓存**：响应时间 < 1ms，无网络开销
2. **命中Redis缓存**：响应时间 1-5ms，避免远程调用
3. **需要远程调用**：仅在缓存未命中时发生，并会更新缓存

### 预期性能提升
- **缓存命中率**：预计90%以上的请求可以通过缓存快速过滤
- **响应时间**：被过滤请求的响应时间从平均50ms降低到1ms以内
- **系统负载**：减少90%以上的不必要远程接口调用

## 配置说明

### application.yml 配置
```yaml
traffic:
  filter:
    customer:
      ids: "3,100,200"  # 需要过滤的客户ID列表
```

### 管理接口
- `POST /api/traffic/filter/refresh` - 刷新过滤配置
- `GET /api/traffic/filter/config` - 获取当前配置
- `GET /api/traffic/filter/check/{customerId}` - 检查客户ID过滤状态

## 部署建议

1. **灰度发布**：建议先在测试环境验证，然后灰度发布到生产环境
2. **监控指标**：添加缓存命中率、过滤数量等监控指标
3. **配置管理**：建议通过配置中心管理过滤规则，支持动态调整

## 注意事项

1. **缓存一致性**：本地缓存设置较短的过期时间（10分钟），避免数据不一致
2. **内存使用**：本地缓存限制最大条目数（50000），防止内存溢出
3. **日志记录**：对过滤的请求进行采样日志记录（1‰概率），便于监控和调试
4. **兜底机制**：即使缓存失效，仍会通过原有逻辑获取数据并更新缓存

## 扩展性

该方案具有良好的扩展性：
- 可以轻松添加更多过滤条件（如按IP、按时间段等）
- 支持更复杂的过滤规则（如正则表达式、范围过滤等）
- 可以集成到监控系统中，实时查看过滤效果
