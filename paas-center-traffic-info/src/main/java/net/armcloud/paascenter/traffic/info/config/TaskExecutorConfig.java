package net.armcloud.paascenter.traffic.info.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class TaskExecutorConfig implements AsyncConfigurer {

    @Bean(name = "trafficDiskExecutor")
    public Executor getTrafficDiskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(40);
        executor.setQueueCapacity(20000);
        executor.setKeepAliveSeconds(3000);
        executor.setThreadNamePrefix("traffic-disk-executor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return executor;
    }


    @Bean(name = "trafficScreenshotExecutor")
    public Executor getTrafficScreenshotExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(40);
        executor.setQueueCapacity(30000);
        executor.setKeepAliveSeconds(3000);
        executor.setThreadNamePrefix("traffic-screenshot-executor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return executor;
    }
}
