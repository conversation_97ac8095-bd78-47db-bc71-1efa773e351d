package net.armcloud.paascenter.traffic.info.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 流量数据过滤管理控制器
 * 用于动态管理需要过滤的客户ID
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/traffic/filter")
@Api(tags = "流量数据过滤管理")
public class TrafficFilterController {

    @Autowired
    private PadTrafficInfoServiceImpl padTrafficInfoService;

    /**
     * 刷新过滤配置
     */
    @PostMapping("/refresh")
    @ApiOperation("刷新过滤配置")
    public Result<String> refreshFilterConfig() {
        try {
            padTrafficInfoService.refreshFilterCustomerIds();
            return Result.success("过滤配置刷新成功");
        } catch (Exception e) {
            log.error("刷新过滤配置失败", e);
            return Result.error("刷新过滤配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前过滤配置
     */
    @GetMapping("/config")
    @ApiOperation("获取当前过滤配置")
    public Result<Map<String, Object>> getFilterConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            // 这里可以添加获取当前过滤配置的逻辑
            config.put("message", "请查看应用配置文件中的 traffic.filter.customer.ids 配置项");
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取过滤配置失败", e);
            return Result.error("获取过滤配置失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定客户ID是否被过滤
     */
    @GetMapping("/check/{customerId}")
    @ApiOperation("检查指定客户ID是否被过滤")
    public Result<Map<String, Object>> checkCustomerFilter(
            @ApiParam("客户ID") @PathVariable Long customerId) {
        try {
            boolean isFiltered = padTrafficInfoService.shouldFilterCustomerId(customerId);
            Map<String, Object> result = new HashMap<>();
            result.put("customerId", customerId);
            result.put("isFiltered", isFiltered);
            result.put("message", isFiltered ? "该客户ID的流量数据会被过滤" : "该客户ID的流量数据会正常处理");
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查客户过滤状态失败", e);
            return Result.error("检查客户过滤状态失败: " + e.getMessage());
        }
    }
}
