package net.armcloud.paascenter.traffic.info.performance;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.service.impl.PadTrafficInfoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 流量数据过滤性能测试
 * 用于验证优化方案的性能提升效果
 */
@Slf4j
class TrafficFilterPerformanceTest {

    private PadTrafficInfoServiceImpl padTrafficInfoService;

    @BeforeEach
    void setUp() {
        padTrafficInfoService = new PadTrafficInfoServiceImpl();
        // 设置过滤配置
        ReflectionTestUtils.setField(padTrafficInfoService, "filterCustomerIds", "3,100,200");
        padTrafficInfoService.initFilterCustomerIds();
    }

    @Test
    void testFilterPerformance() throws InterruptedException {
        int threadCount = 100;
        int requestsPerThread = 1000;
        int totalRequests = threadCount * requestsPerThread;

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        AtomicLong filteredCount = new AtomicLong(0);
        AtomicLong totalTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    long threadStartTime = System.nanoTime();
                    
                    for (int j = 0; j < requestsPerThread; j++) {
                        // 模拟不同的客户ID，其中30%是需要过滤的
                        Long customerId = (long) ((threadId * requestsPerThread + j) % 10);
                        if (customerId == 3L) {
                            customerId = 3L; // 需要过滤的ID
                        }
                        
                        boolean isFiltered = padTrafficInfoService.shouldFilterCustomerId(customerId);
                        if (isFiltered) {
                            filteredCount.incrementAndGet();
                        }
                    }
                    
                    long threadEndTime = System.nanoTime();
                    totalTime.addAndGet(threadEndTime - threadStartTime);
                    
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 输出性能统计
        log.info("=== 流量数据过滤性能测试结果 ===");
        log.info("总请求数: {}", totalRequests);
        log.info("过滤请求数: {}", filteredCount.get());
        log.info("过滤率: {:.2f}%", (double) filteredCount.get() / totalRequests * 100);
        log.info("总耗时: {} ms", totalDuration);
        log.info("平均响应时间: {:.3f} ms", (double) totalDuration / totalRequests);
        log.info("QPS: {:.0f}", (double) totalRequests / totalDuration * 1000);
        log.info("平均每个过滤操作耗时: {:.3f} ns", (double) totalTime.get() / totalRequests);
    }

    @Test
    void testCachePerformance() {
        int iterations = 100000;
        String[] padOutCodes = {
            "pad-001", "pad-002", "pad-003", "pad-004", "pad-005",
            "pad-006", "pad-007", "pad-008", "pad-009", "pad-010"
        };

        // 预热本地缓存
        for (String padOutCode : padOutCodes) {
            padTrafficInfoService.cacheCustomerIdByPadOutCode(padOutCode, 3L);
        }

        long startTime = System.nanoTime();

        for (int i = 0; i < iterations; i++) {
            String padOutCode = padOutCodes[i % padOutCodes.length];
            Long customerId = padTrafficInfoService.getCustomerIdByPadOutCode(padOutCode);
            // 验证缓存命中
            assert customerId != null && customerId == 3L;
        }

        long endTime = System.nanoTime();
        long duration = endTime - startTime;

        log.info("=== 本地缓存性能测试结果 ===");
        log.info("测试次数: {}", iterations);
        log.info("总耗时: {:.3f} ms", duration / 1_000_000.0);
        log.info("平均每次查询耗时: {:.3f} ns", (double) duration / iterations);
        log.info("每秒查询次数: {:.0f}", (double) iterations / duration * 1_000_000_000);
    }

    @Test
    void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        System.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 添加大量缓存数据
        int cacheSize = 50000;
        for (int i = 0; i < cacheSize; i++) {
            String padOutCode = "pad-" + String.format("%06d", i);
            Long customerId = (long) (i % 1000); // 模拟1000个不同的客户ID
            padTrafficInfoService.cacheCustomerIdByPadOutCode(padOutCode, customerId);
        }
        
        // 记录缓存后内存使用
        System.gc();
        long afterCacheMemory = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryIncrease = afterCacheMemory - initialMemory;
        
        log.info("=== 内存使用测试结果 ===");
        log.info("缓存条目数: {}", cacheSize);
        log.info("初始内存使用: {:.2f} MB", initialMemory / 1024.0 / 1024.0);
        log.info("缓存后内存使用: {:.2f} MB", afterCacheMemory / 1024.0 / 1024.0);
        log.info("内存增长: {:.2f} MB", memoryIncrease / 1024.0 / 1024.0);
        log.info("平均每个缓存条目内存占用: {:.2f} bytes", (double) memoryIncrease / cacheSize);
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        int threadCount = 50;
        int operationsPerThread = 1000;
        String padOutCode = "concurrent-test-pad";
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        AtomicLong successCount = new AtomicLong(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 交替进行读写操作
                        if (j % 2 == 0) {
                            // 写操作
                            Long customerId = (long) (threadId % 10);
                            padTrafficInfoService.cacheCustomerIdByPadOutCode(
                                padOutCode + "-" + threadId, customerId);
                        } else {
                            // 读操作
                            Long customerId = padTrafficInfoService.getCustomerIdByPadOutCode(
                                padOutCode + "-" + threadId);
                            if (customerId != null) {
                                successCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("=== 并发访问测试结果 ===");
        log.info("并发线程数: {}", threadCount);
        log.info("每线程操作数: {}", operationsPerThread);
        log.info("总操作数: {}", threadCount * operationsPerThread);
        log.info("成功读取次数: {}", successCount.get());
        log.info("总耗时: {} ms", duration);
        log.info("平均操作耗时: {:.3f} ms", (double) duration / (threadCount * operationsPerThread));
    }
}
