package net.armcloud.paascenter.traffic.info.service.impl;

import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;
import net.armcloud.paascenter.traffic.info.redis.service.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * PadTrafficInfoServiceImpl 测试类
 * 主要测试流量数据过滤优化功能
 */
@ExtendWith(MockitoExtension.class)
class PadTrafficInfoServiceImplTest {

    @Mock
    private RedisService redisService;

    @InjectMocks
    private PadTrafficInfoServiceImpl padTrafficInfoService;

    @BeforeEach
    void setUp() {
        // 设置过滤的客户ID配置
        ReflectionTestUtils.setField(padTrafficInfoService, "filterCustomerIds", "3,100,200");
        // 初始化过滤配置
        padTrafficInfoService.initFilterCustomerIds();
    }

    @Test
    void testShouldFilterCustomerId() {
        // 测试需要过滤的客户ID
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(3L));
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(100L));
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(200L));

        // 测试不需要过滤的客户ID
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(1L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(2L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(99L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(null));
    }

    @Test
    void testGetCustomerIdByPadOutCode_FromCache() {
        // 模拟Redis缓存中有数据
        String padOutCode = "test-pad-001";
        Long expectedCustomerId = 3L;
        
        when(redisService.getCacheObject(anyString())).thenReturn(expectedCustomerId.toString());
        
        Long actualCustomerId = padTrafficInfoService.getCustomerIdByPadOutCode(padOutCode);
        
        assertEquals(expectedCustomerId, actualCustomerId);
    }

    @Test
    void testGetCustomerIdByPadOutCode_CacheEmpty() {
        // 模拟Redis缓存中没有数据
        String padOutCode = "test-pad-002";
        
        when(redisService.getCacheObject(anyString())).thenReturn(null);
        
        Long actualCustomerId = padTrafficInfoService.getCustomerIdByPadOutCode(padOutCode);
        
        assertNull(actualCustomerId);
    }

    @Test
    void testRefreshFilterCustomerIds() {
        // 测试刷新过滤配置
        ReflectionTestUtils.setField(padTrafficInfoService, "filterCustomerIds", "5,10,15");
        
        padTrafficInfoService.refreshFilterCustomerIds();
        
        // 验证新的过滤配置生效
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(5L));
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(10L));
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(15L));
        
        // 验证旧的过滤配置失效
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(3L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(100L));
    }

    @Test
    void testRefreshFilterCustomerIds_InvalidConfig() {
        // 测试无效的配置
        ReflectionTestUtils.setField(padTrafficInfoService, "filterCustomerIds", "1,invalid,3");
        
        padTrafficInfoService.refreshFilterCustomerIds();
        
        // 验证有效的ID被正确解析
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(1L));
        assertTrue(padTrafficInfoService.shouldFilterCustomerId(3L));
        
        // 验证无效的ID被忽略
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(null));
    }

    @Test
    void testRefreshFilterCustomerIds_EmptyConfig() {
        // 测试空配置
        ReflectionTestUtils.setField(padTrafficInfoService, "filterCustomerIds", "");
        
        padTrafficInfoService.refreshFilterCustomerIds();
        
        // 验证所有ID都不被过滤
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(1L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(3L));
        assertFalse(padTrafficInfoService.shouldFilterCustomerId(100L));
    }

    /**
     * 创建测试用的TrafficDataDTO
     */
    private TrafficDataDTO createTestTrafficDataDTO(String padOutCode) {
        TrafficDataDTO dto = new TrafficDataDTO();
        dto.setPadOutCode(padOutCode);
        dto.setPadIp("*************");
        dto.setPublicOut(1000L);
        dto.setPublicIn(2000L);
        dto.setPrivateOut(500L);
        dto.setPrivateIn(1500L);
        dto.setTotalOut(1500L);
        dto.setTotalIn(3500L);
        dto.setBillingTime(new Date());
        return dto;
    }
}
